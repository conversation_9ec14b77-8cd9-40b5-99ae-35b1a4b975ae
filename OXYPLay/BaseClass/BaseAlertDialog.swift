//
//  BaseAlertDialog.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 通用中间弹窗基类
class BaseAlertDialog: UIViewController {
    
    // MARK: - Properties
    
    /// 存储订阅
    var cancellables = Set<AnyCancellable>()
    
    /// 左按钮点击回调
    var leftButtonAction: (() -> Void)?
    
    /// 右按钮点击回调
    var rightButtonAction: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 背景遮罩
    private lazy var backgroundView = UIView().then {
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        $0.alpha = 0
    }
    
    /// 弹窗容器
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
        $0.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        $0.alpha = 0
    }
    
    /// 关闭按钮
    private lazy var closeButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "closebuttonicon"), for: .normal)
        $0.tintColor = color_2B2C2F40
    }
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.textAlignment = .center
        $0.numberOfLines = 0
    }
    
    /// 内容容器
    lazy var contentView = UIView().then {
        $0.backgroundColor = .clear
    }
    
    /// 按钮容器
    private lazy var buttonContainerView = UIView().then {
        $0.backgroundColor = .clear
    }
    
    /// 左按钮
    private lazy var leftButton = BaseButton().then {
        $0.setTitle("取消", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.backgroundColor = color_F6F8F9
        $0.isRounded = true
    }
    
    /// 右按钮
    private lazy var rightButton = BaseButton().then {
        $0.setTitle("确定", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }
    
    // MARK: - Initialization
    
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        modalPresentationStyle = .overFullScreen
        modalTransitionStyle = .crossDissolve
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        showAnimation()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = .clear
        
        view.addSubview(backgroundView)
        view.addSubview(containerView)
        
        containerView.addSubview(closeButton)
        containerView.addSubview(titleLabel)
        containerView.addSubview(contentView)
        containerView.addSubview(buttonContainerView)
        
        buttonContainerView.addSubview(leftButton)
        buttonContainerView.addSubview(rightButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 背景遮罩
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 弹窗容器
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(280)
            make.height.greaterThanOrEqualTo(160)
        }
        
        // 关闭按钮
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(14)
            make.right.equalTo(-14)
            make.width.height.equalTo(24)
        }
        
        // 标题
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(20)
            make.right.equalTo(-20)
        }
        
        // 内容视图
        contentView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.bottom.equalTo(buttonContainerView.snp.top).offset(-20)
        }
        
        // 按钮容器
        buttonContainerView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.bottom.equalTo(-20)
            make.height.equalTo(44)
        }
        
        // 左按钮
        leftButton.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(rightButton.snp.left).offset(-12)
            make.width.equalTo(rightButton)
        }
        
        // 右按钮
        rightButton.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
        }
    }
    
    private func setupBindings() {
        // 背景点击关闭
        let backgroundTap = UITapGestureRecognizer()
        backgroundTap.tapPublisher
            .sink { [weak self] _ in
                self?.dismiss(animated: true)
            }
            .store(in: &cancellables)
        backgroundView.addGestureRecognizer(backgroundTap)
        
        // 关闭按钮
        closeButton.tapPublisher
            .sink { [weak self] _ in
                self?.dismiss(animated: true)
            }
            .store(in: &cancellables)
        
        // 左按钮
        leftButton.tapPublisher
            .sink { [weak self] _ in
                self?.leftButtonAction?()
            }
            .store(in: &cancellables)
        
        // 右按钮
        rightButton.tapPublisher
            .sink { [weak self] _ in
                self?.rightButtonAction?()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 配置弹窗
    /// - Parameters:
    ///   - title: 标题
    ///   - leftButtonTitle: 左按钮标题
    ///   - rightButtonTitle: 右按钮标题
    ///   - showCloseButton: 是否显示关闭按钮
    func configure(
        title: String,
        leftButtonTitle: String = "取消",
        rightButtonTitle: String = "确定",
        showCloseButton: Bool = true
    ) {
        titleLabel.text = title
        leftButton.setTitle(leftButtonTitle, for: .normal)
        rightButton.setTitle(rightButtonTitle, for: .normal)
        closeButton.isHidden = !showCloseButton
    }
    
    /// 显示弹窗
    /// - Parameter viewController: 父控制器
    func show(in viewController: UIViewController) {
        viewController.present(self, animated: false)
    }
    
    /// 隐藏按钮容器（仅显示内容）
    func hideButtons() {
        buttonContainerView.isHidden = true
        contentView.snp.updateConstraints { make in
            make.bottom.equalTo(-20)
        }
    }
    
    // MARK: - Animation
    
    private func showAnimation() {
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0) {
            self.backgroundView.alpha = 1
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        }
    }
    
    override func dismiss(animated flag: Bool, completion: (() -> Void)? = nil) {
        if flag {
            UIView.animate(withDuration: 0.2) {
                self.backgroundView.alpha = 0
                self.containerView.alpha = 0
                self.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            } completion: { _ in
                super.dismiss(animated: false, completion: completion)
            }
        } else {
            super.dismiss(animated: false, completion: completion)
        }
    }
}
