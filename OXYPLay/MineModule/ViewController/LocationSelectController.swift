//
//  LocationSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 地区选择控制器（简化版，只支持两级选择）
class LocationSelectController: BasePresentController {
    
    // MARK: - Properties
    
    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<(String, Bool), Never>()

    /// 是否公开显示
    private var isPublicDisplay: Bool = true
    
    /// ViewModel
    private let viewModel = LocationSelectViewModel()
    
    /// 当前选中的省份
    private var selectedProvince: RegionListItemModel?
    
    /// 当前选中的城市
    private var selectedCity: RegionListItemModel?
    
    /// 分组后的地区数据
    private var groupedRegions: [String: [RegionListItemModel]] = [:]
    
    /// 排序后的分组键
    private var sortedSectionKeys: [String] = []
    
    // MARK: - UI Components

    /// 自定义位置选择视图
    private lazy var locationSelectionView = LocationSelectionView().then {
        $0.delegate = self
    }

    /// 公开显示容器
    private lazy var publicDisplayContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
    }
    
    /// 公开显示标签
    private lazy var publicDisplayLabel = UILabel().then {
        $0.text = "是否公开展示"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_2B2C2F80
    }
    
    /// 显示性别标签标签
    private lazy var showGenderLabelLabel = UILabel().then {
        $0.text = "展示位置标签"
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.textColor = color_2B2C2F
    }
    private lazy var showLocationLabelSwitch = UISwitch().then {
        $0.onTintColor = color_blue
        $0.isOn = true
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupLocationUI()
        setupLocationBindings()
        loadInitialData()
    }
    
    // MARK: - Setup
    
    private func setupLocationUI() {
        configView(title: "选择地区", bottomTitle: "确认")

        contentView.addSubview(locationSelectionView)

        // 添加公开显示容器
        contentView.addSubview(publicDisplayContainer)
        contentView.addSubview(publicDisplayLabel)
        publicDisplayContainer.addSubview(showGenderLabelLabel)
        publicDisplayContainer.addSubview(showLocationLabelSwitch)
        setupLocationConstraints()
    }
    
    private func setupLocationConstraints() {
        // 自定义位置选择视图
        locationSelectionView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(360)
        }

        publicDisplayLabel.snp.makeConstraints { make in
            make.top.equalTo(locationSelectionView.snp.bottom).offset(12)
            make.left.equalTo(12)
        }
        // 公开显示容器约束
        publicDisplayContainer.snp.makeConstraints { make in
            make.top.equalTo(publicDisplayLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(37)
        }

        // 公开显示标签约束
        showGenderLabelLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
        }

        // 显示性别标签开关约束
        showLocationLabelSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
    }
    
    private func setupLocationBindings() {
        // 设置自定义视图的事件绑定
        locationSelectionView.setupBindings()

        // 监听地区数据变化
        viewModel.$regionList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] regions in
                self?.processRegionData(regions)
                self?.locationSelectionView.updateRegionData(regions, groupedRegions: self?.groupedRegions ?? [:], sortedSectionKeys: self?.sortedSectionKeys ?? [])
            }
            .store(in: &cancellables)

        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Loading
    
    private func loadInitialData() {
        // 初始化UI状态
        locationSelectionView.resetToProvinceSelection()

        // 加载省份数据
        viewModel.fetchRegionList(parent_code: nil)
    }
    
    // MARK: - Button Actions

    private func provinceButtonTapped() {
        // 重新加载省份数据
        viewModel.fetchRegionList(parent_code: nil)
        locationSelectionView.switchToProvinceSelection()

        // 重置选择状态
        selectedCity = nil
    }

    private func cityButtonTapped() {
        guard let province = selectedProvince else { return }

        // 加载省份下的城市数据
        viewModel.fetchRegionList(parent_code: province.code)
        locationSelectionView.switchToCitySelection()
    }
    
    // MARK: - Private Methods
    
    /// 处理地区数据分组
    /// - Parameter regions: 地区数据
    private func processRegionData(_ regions: [RegionListItemModel]) {
        groupedRegions.removeAll()
        
        for region in regions {
            let firstChar = String(region.name.prefix(1))
            if groupedRegions[firstChar] == nil {
                groupedRegions[firstChar] = []
            }
            groupedRegions[firstChar]?.append(region)
        }
        
        sortedSectionKeys = groupedRegions.keys.sorted()
    }
    
    /// 确认选择
    private func confirmSelection() {
        var locationString = ""

        if let province = selectedProvince {
            locationString += province.name
        }

        if let city = selectedCity, city.name != selectedProvince?.name {
            locationString += city.name
        }

        if !locationString.isEmpty {
            selectionCompletedPublisher.send((locationString, showLocationLabelSwitch.isOn))
            dismiss(animated: true)
        }
    }
    
    /// 判断地区是否被选中
    /// - Parameter region: 地区模型
    /// - Returns: 是否选中
    private func isRegionSelected(region: RegionListItemModel) -> Bool {
        if let selectedProvince = selectedProvince, selectedProvince.code == region.code {
            return true
        }
        if let selectedCity = selectedCity, selectedCity.code == region.code {
            return true
        }
        return false
    }
    
    /// 处理省份选择
    /// - Parameter region: 选中的省份
    private func handleProvinceSelection(region: RegionListItemModel) {
        selectedProvince = region
        selectedCity = nil

        // 更新自定义视图的省份选择
        locationSelectionView.updateProvinceSelection(region.name)

        if region.has_children {
            // 显示城市选择项
            locationSelectionView.showCitySelection()

            // 加载城市数据
            viewModel.fetchRegionList(parent_code: region.code)
            locationSelectionView.switchToCitySelection()
        } else {
            // 直辖市情况，直接完成选择
            confirmSelection()
        }
    }

    /// 处理城市选择
    /// - Parameter region: 选中的城市
    private func handleCitySelection(region: RegionListItemModel) {
        selectedCity = region

        // 更新自定义视图的城市选择
        locationSelectionView.updateCitySelection(region.name)
    }
}

// MARK: - LocationSelectionViewDelegate

extension LocationSelectController: LocationSelectionViewDelegate {
    func locationSelectionView(_ view: LocationSelectionView, didSelectRegion region: RegionListItemModel) {
        // 根据当前状态判断是选择省份还是城市
        if selectedProvince == nil {
            handleProvinceSelection(region: region)
        } else {
            handleCitySelection(region: region)
        }
    }

    func locationSelectionViewDidTapProvinceButton(_ view: LocationSelectionView) {
        provinceButtonTapped()
    }

    func locationSelectionViewDidTapCityButton(_ view: LocationSelectionView) {
        cityButtonTapped()
    }

    func locationSelectionView(_ view: LocationSelectionView, isRegionSelected region: RegionListItemModel) -> Bool {
        return isRegionSelected(region: region)
    }
}

// MARK: - Public Methods

extension LocationSelectController {
    /// 配置地区选择弹窗
    /// - Parameters:
    ///   - currentLocation: 当前地区
    ///   - isPublicDisplay: 是否公开显示
    func configure(currentLocation: String, isPublicDisplay: Bool) {
        self.isPublicDisplay = isPublicDisplay
        showLocationLabelSwitch.isOn = isPublicDisplay
        // 这里可以根据currentLocation设置初始选择状态
    }
}
