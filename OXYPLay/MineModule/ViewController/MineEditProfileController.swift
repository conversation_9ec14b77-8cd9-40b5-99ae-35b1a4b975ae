//
//  MineEditProfileController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import Combine
import CombineCocoa
import Then
import SnapKit
import Kingfisher

class MineEditProfileController: BaseViewController {

    // MARK: - Properties

    private let viewModel = MineEditProfileViewModel()

    // MARK: - UI Components

    /// 头像图片
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.layer.cornerRadius = 35
        $0.masksToBounds = true
        $0.backgroundColor = color_F6F8F9
    }

    /// 编辑头像按钮
    private lazy var editAvatarButton = BaseButton().then {
        $0.setTitle("编辑头像", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.isRounded = true
        $0.horizontalPadding = 10
        $0.verticalPadding = 6
    }

    /// 表单列表视图
    private lazy var listView = BaseListView().then {
        $0.delegate = self
    }

    /// 保存按钮
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
        
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        loadUserData()
    }

    override func configUI() {
        title = "编辑资料"
        view.backgroundColor = color_F6F8F9

        view.addSubview(avatarImageView)
        view.addSubview(editAvatarButton)
        view.addSubview(listView)
        view.addSubview(toolBar)
    }

    override func configLayout() {
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "保存")

        // 头像图片
        avatarImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(view.safeAreaLayoutGuide).offset(12)
            make.width.height.equalTo(70)
        }

        // 编辑头像按钮
        editAvatarButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(avatarImageView.snp.bottom).offset(10)
        }

        // 表单列表
        listView.snp.makeConstraints { make in
            make.top.equalTo(editAvatarButton.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top).offset(-12)
        }

        // 保存按钮
        toolBar.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.bottom.equalTo(0)
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }

    override func setupBindings() {
        // 编辑头像按钮点击
        editAvatarButton.tapPublisher
            .sink { [weak self] _ in
               
            }
            .store(in: &cancellables)

        // 监听用户信息更新
        viewModel.$userInfo
            .receive(on: DispatchQueue.main)
            .sink { [weak self] userInfo in
                self?.updateUI(with: userInfo)
            }
            .store(in: &cancellables)

        // 监听保存结果
        viewModel.saveResultPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    self?.showSuccessAlert("保存成功") {
                        self?.navigationController?.popViewController(animated: true)
                    }
                } else {
                    self?.showErrorAlert("保存失败，请重试")
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Private Methods

    /// 加载用户数据
    private func loadUserData() {
        viewModel.loadUserInfo()
        setupFormItems()
    }

    /// 设置表单项
    private func setupFormItems() {
        let configs = [
            [
                ListItemConfig(type: .titleInput, identifier: "nickname", placeholder: "请填写昵称", title: "名字"),
                ListItemConfig(type: .select, identifier: "gender", placeholder: "男", title: "性别"),
                ListItemConfig(type: .select, identifier: "ski_age", placeholder: "请选择您的雪龄", title: "雪龄"),
                ListItemConfig(type: .select, identifier: "location", placeholder: "请选择您的所在地区", title: "地区")
            ],
            [
                ListItemConfig(type: .content, identifier: "description", placeholder: "请输入您的个人简介", title: "个人简介",maxLength: 20)
            ]
        ]

        listView.setItems(configs)
    }

    /// 更新UI显示
    private func updateUI(with userInfo: UserModel?) {
        guard let userInfo = userInfo else { return }

        avatarImageView.setImage(url: userInfo.avatar)
        // 更新表单数据
        let updatedConfigs = [
            [ListItemConfig(type: .titleInput, identifier: "nickname", data: userInfo.nickname, placeholder:"请填写昵称", title: "名字", subTitle: userInfo.nickname )],
            [
                ListItemConfig(type: .select, identifier: "gender", data: userInfo.gender, title: "性别", subTitle: userInfo.gender == 1 ? "男" : "女"),
                ListItemConfig(type: .select, identifier: "ski_age", data: userInfo.ski_age, placeholder:"请选择您的雪龄", title: "雪龄", subTitle: userInfo.ski_age),
                ListItemConfig(type: .select, identifier: "location", data: userInfo.location, placeholder: "请选择您的所在地区", title: "地区", subTitle: userInfo.location)
            ],
            [
                ListItemConfig(type: .content, identifier: "description", data: userInfo.description, placeholder: "请输入您的个人简介", title: "个人简介",maxLength: 20)
            ]
        ]

        listView.setItems(updatedConfigs)
    }

    /// 保存资料
    private func saveProfile() {
        // 获取表单数据
        let formData = listView.getAllData()

        // 验证必填项
        guard listView.validateAllItems() else {
            return
        }

        // 显示确认弹窗
        showConfirmDialog()
    }

    /// 显示确认保存弹窗
    private func showConfirmDialog() {
        let dialog = BaseAlertDialog()
        dialog.configure(
            title: "是否保存修改?",
            leftButtonTitle: "取消",
            rightButtonTitle: "保存"
        )

        dialog.leftButtonAction = {
            dialog.dismiss(animated: true)
        }

        dialog.rightButtonAction = { [weak self] in
            dialog.dismiss(animated: true)
            self?.performSave()
        }

        dialog.show(in: self)
    }

    /// 执行保存操作
    private func performSave() {
        let formData = listView.getAllData()
        viewModel.saveUserInfo(formData)
    }
}

// MARK: - BaseListViewDelegate

extension MineEditProfileController: BaseListViewDelegate {
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        switch config.identifier {
        case "gender":
            showGenderSelectDialog(currentGender: config.data as? Int ?? 1)
        case "ski_age":
            showSkiAgeSelectDialog(currentValue: config.data as? String ?? "")
        case "location":
            showLocationSelectDialog()
        default:
            break
        }
    }

    /// 显示性别选择弹窗
    private func showGenderSelectDialog(currentGender: Int) {
        let dialog = GenderSelectDialog(currentGender: currentGender, isPublicDisplay: true)

        dialog.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (selectedGender, isPublicDisplay) in
                let displayText = selectedGender == 1 ? "男" : "女"
                self?.updateFormItem(identifier: "gender", value: selectedGender, displayText: displayText)
            dialog.dismiss(animated: true)
                // 这里可以处理isPublicDisplay
            }
            .store(in: &cancellables)

        customPresent(dialog, animated: true)
    }

    /// 显示雪龄选择弹窗
    private func showSkiAgeSelectDialog(currentValue: String) {
        let dialog = SkiAgeSelectDialog(currentSkiAge: currentValue, isPublicDisplay: true)

        dialog.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (skiAge, isPublicDisplay) in
                self?.updateFormItem(identifier: "ski_age", value: skiAge, displayText: skiAge)
            dialog.dismiss(animated: true)
                // 这里可以处理isPublicDisplay
            }
            .store(in: &cancellables)

        customPresent(dialog, animated: true)
    }

    /// 显示地区选择弹窗
    private func showLocationSelectDialog() {
        let locationController = LocationSelectController()
        locationController.configure(currentLocation: "", isPublicDisplay: true)

        locationController.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (location, isPublicDisplay) in
                self?.updateFormItem(identifier: "location", value: location, displayText: location)
                // 这里可以处理isPublicDisplay
            }
            .store(in: &cancellables)

        customPresent(locationController, animated: true)
    }

    /// 更新表单项数据
    private func updateFormItem(identifier: String, value: Any, displayText: String) {
        let currentItem = listView.getItem(identifier: identifier)
        if var config = currentItem.itemConfig {
            config.data = value
            config.subTitle = displayText
            listView.updateItem(with: config)
        }
    }
}
extension MineEditProfileController:TabToolBarDelegate{
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?){
        
    }

}
